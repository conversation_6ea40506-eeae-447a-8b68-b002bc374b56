package com.mdsadrulhasan.appy99lisence;

import android.content.Intent;
import android.os.Bundle;
import android.widget.Button;
import android.widget.Toast;

import androidx.appcompat.app.AppCompatActivity;

/**
 * Test activity to launch DashboardActivity with proper intent data
 * This helps debug the dashboard display issues
 */
public class TestDashboardActivity extends AppCompatActivity {

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        
        // Create a simple layout with a button
        Button testButton = new Button(this);
        testButton.setText("Launch Dashboard");
        testButton.setOnClickListener(v -> launchDashboard());
        
        setContentView(testButton);
    }
    
    private void launchDashboard() {
        try {
            Intent dashboardIntent = new Intent(this, DashboardActivity.class);
            
            // Add test data
            dashboardIntent.putExtra(DashboardActivity.EXTRA_LICENSE_KEY, "TEST-LICENSE-KEY-12345");
            dashboardIntent.putExtra(DashboardActivity.EXTRA_DOMAIN_URL, "https://demo.appystore.com");
            dashboardIntent.putExtra(DashboardActivity.EXTRA_ACTIVATION_STATUS, true);
            dashboardIntent.putExtra(DashboardActivity.EXTRA_EXPIRATION_TIMESTAMP, System.currentTimeMillis() + (30L * 24 * 60 * 60 * 1000));
            dashboardIntent.putExtra(DashboardActivity.EXTRA_DEVICE_ID, "TEST-DEVICE-" + System.currentTimeMillis());
            dashboardIntent.putExtra(DashboardActivity.EXTRA_DEVICE_INFO, "Test Device Info");
            
            startActivity(dashboardIntent);
            
            Toast.makeText(this, "Launching dashboard with test data...", Toast.LENGTH_SHORT).show();
            
        } catch (Exception e) {
            Toast.makeText(this, "Error launching dashboard: " + e.getMessage(), Toast.LENGTH_LONG).show();
        }
    }
}
