package com.mdsadrulhasan.appy99lisence.fragments;

import android.content.SharedPreferences;
import android.os.Bundle;
import android.preference.PreferenceManager;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.TextView;
import android.widget.Toast;
import android.widget.ToggleButton;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.fragment.app.Fragment;
import com.mdsadrulhasan.appy99lisence.R;

public class DashboardHomeFragment extends Fragment {

    private static final String TAG = "DashboardHomeFragment";

    // UI Components
    private TextView deviceIdText;
    private TextView serviceStatusText;
    private Button startServiceButton;
    private Button stopServiceButton;
    private TextView simAStatus;
    private TextView simBStatus;
    private ToggleButton smsServiceToggle;

    // New UI Components
    private TextView systemStatusIndicator;
    private TextView totalRechargesCount;
    private TextView successRateText;
    private TextView uptimeText;

    // Data from parent activity
    private String licenseKey;
    private String domainUrl;
    private boolean activationStatus;
    private long expirationTimestamp;
    private String deviceId;
    private String deviceInfo;

    private SharedPreferences preferences;

    public static DashboardHomeFragment newInstance(String licenseKey, String domainUrl,
                                                   boolean activationStatus, long expirationTimestamp,
                                                   String deviceId, String deviceInfo) {
        DashboardHomeFragment fragment = new DashboardHomeFragment();
        Bundle args = new Bundle();
        args.putString("license_key", licenseKey);
        args.putString("domain_url", domainUrl);
        args.putBoolean("activation_status", activationStatus);
        args.putLong("expiration_timestamp", expirationTimestamp);
        args.putString("device_id", deviceId);
        args.putString("device_info", deviceInfo);
        fragment.setArguments(args);
        return fragment;
    }

    @Override
    public void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);

        if (getArguments() != null) {
            licenseKey = getArguments().getString("license_key");
            domainUrl = getArguments().getString("domain_url");
            activationStatus = getArguments().getBoolean("activation_status");
            expirationTimestamp = getArguments().getLong("expiration_timestamp");
            deviceId = getArguments().getString("device_id");
            deviceInfo = getArguments().getString("device_info");
        }

        preferences = PreferenceManager.getDefaultSharedPreferences(requireContext());
    }

    @Nullable
    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, @Nullable ViewGroup container,
                           @Nullable Bundle savedInstanceState) {
        return inflater.inflate(R.layout.dashboard_home_content, container, false);
    }

    @Override
    public void onViewCreated(@NonNull View view, @Nullable Bundle savedInstanceState) {
        super.onViewCreated(view, savedInstanceState);

        try {
            Log.d(TAG, "DashboardHomeFragment onViewCreated");
            initializeViews(view);
            setupClickListeners();
            loadDashboardData();
            Log.d(TAG, "DashboardHomeFragment initialization completed");
        } catch (Exception e) {
            Log.e(TAG, "Error in onViewCreated", e);
        }
    }

    private void initializeViews(View view) {
        Log.d(TAG, "Initializing views in DashboardHomeFragment");

        // Original UI components
        deviceIdText = view.findViewById(R.id.device_id_text);
        serviceStatusText = view.findViewById(R.id.service_status_text);
        startServiceButton = view.findViewById(R.id.start_service_button);
        stopServiceButton = view.findViewById(R.id.stop_service_button);
        simAStatus = view.findViewById(R.id.sim_a_status);
        simBStatus = view.findViewById(R.id.sim_b_status);
        smsServiceToggle = view.findViewById(R.id.sms_service_toggle);

        // New UI components
        systemStatusIndicator = view.findViewById(R.id.system_status_indicator);
        totalRechargesCount = view.findViewById(R.id.total_recharges_count);
        successRateText = view.findViewById(R.id.success_rate_text);
        uptimeText = view.findViewById(R.id.uptime_text);

        // Log which views were found
        Log.d(TAG, "Views found - DeviceId: " + (deviceIdText != null) +
                   ", ServiceStatus: " + (serviceStatusText != null) +
                   ", StartButton: " + (startServiceButton != null) +
                   ", StopButton: " + (stopServiceButton != null) +
                   ", SimA: " + (simAStatus != null) +
                   ", SimB: " + (simBStatus != null) +
                   ", SmsToggle: " + (smsServiceToggle != null) +
                   ", SystemStatus: " + (systemStatusIndicator != null) +
                   ", TotalRecharges: " + (totalRechargesCount != null) +
                   ", SuccessRate: " + (successRateText != null) +
                   ", Uptime: " + (uptimeText != null));
    }

    private void setupClickListeners() {
        if (startServiceButton != null) {
            startServiceButton.setOnClickListener(v -> startTelecomService());
        }

        if (stopServiceButton != null) {
            stopServiceButton.setOnClickListener(v -> stopTelecomService());
        }

        if (smsServiceToggle != null) {
            smsServiceToggle.setOnCheckedChangeListener((buttonView, isChecked) -> {
                preferences.edit().putBoolean("sms_service_enabled", isChecked).apply();
                updateServiceStatus();
            });
        }
    }

    private void loadDashboardData() {
        // Load device information
        loadDeviceInfo();

        // Load service status
        updateServiceStatus();

        // Load SIM information
        loadSimInfo();

        // Load SMS service setting
        loadSmsServiceSetting();

        // Load dashboard statistics
        loadDashboardStatistics();
    }

    private void loadDeviceInfo() {
        if (deviceIdText != null) {
            // Use the device ID passed from the parent activity, fallback to preferences
            String displayDeviceId = (deviceId != null && !deviceId.isEmpty()) ?
                deviceId : preferences.getString("device_id", "Unknown Device");
            deviceIdText.setText(displayDeviceId);
            Log.d(TAG, "Device ID loaded: " + displayDeviceId);
        } else {
            Log.w(TAG, "Device ID text view is null");
        }
    }

    private void updateServiceStatus() {
        if (serviceStatusText != null) {
            boolean telecomServiceRunning = preferences.getBoolean("telecom_service_running", false);
            boolean smsServiceEnabled = preferences.getBoolean("sms_service_enabled", true);

            String status;
            if (telecomServiceRunning && smsServiceEnabled) {
                status = "Service Status: Running";
                serviceStatusText.setTextColor(getResources().getColor(android.R.color.holo_green_dark));
            } else if (telecomServiceRunning) {
                status = "Service Status: Partial (SMS disabled)";
                serviceStatusText.setTextColor(getResources().getColor(android.R.color.holo_orange_dark));
            } else {
                status = "Service Status: Stopped";
                serviceStatusText.setTextColor(getResources().getColor(android.R.color.holo_red_dark));
            }

            serviceStatusText.setText(status);
        }
    }

    private void loadSimInfo() {
        if (simAStatus != null) {
            String simAInfo = preferences.getString("sim_a_info", "Item 1");
            simAStatus.setText(simAInfo);
        }

        if (simBStatus != null) {
            String simBInfo = preferences.getString("sim_b_info", "Item 1");
            simBStatus.setText(simBInfo);
        }
    }

    private void loadSmsServiceSetting() {
        if (smsServiceToggle != null) {
            boolean smsEnabled = preferences.getBoolean("sms_service_enabled", true);
            smsServiceToggle.setChecked(smsEnabled);
        }
    }

    private void startTelecomService() {
        try {
            // Update preference
            preferences.edit().putBoolean("telecom_service_running", true).apply();

            // Update UI
            updateServiceStatus();

            // Show toast
            Toast.makeText(requireContext(), "Telecom service started", Toast.LENGTH_SHORT).show();

            Log.d(TAG, "Telecom service started from home fragment");
        } catch (Exception e) {
            Log.e(TAG, "Error starting telecom service", e);
            Toast.makeText(requireContext(), "Error starting service", Toast.LENGTH_SHORT).show();
        }
    }

    private void stopTelecomService() {
        try {
            // Update preference
            preferences.edit().putBoolean("telecom_service_running", false).apply();

            // Update UI
            updateServiceStatus();

            // Show toast
            Toast.makeText(requireContext(), "Telecom service stopped", Toast.LENGTH_SHORT).show();

            Log.d(TAG, "Telecom service stopped from home fragment");
        } catch (Exception e) {
            Log.e(TAG, "Error stopping telecom service", e);
            Toast.makeText(requireContext(), "Error stopping service", Toast.LENGTH_SHORT).show();
        }
    }

    private void loadDashboardStatistics() {
        Log.d(TAG, "Loading dashboard statistics");

        // Update system status
        updateSystemStatus();

        // Load statistics from preferences
        updateStatistics();
    }

    private void updateSystemStatus() {
        if (systemStatusIndicator != null) {
            boolean isOnline = preferences.getBoolean("system_online", true);
            if (isOnline) {
                systemStatusIndicator.setText("🟢 System Online");
                systemStatusIndicator.setTextColor(getResources().getColor(android.R.color.holo_green_dark));
            } else {
                systemStatusIndicator.setText("🔴 System Offline");
                systemStatusIndicator.setTextColor(getResources().getColor(android.R.color.holo_red_dark));
            }
        }
    }

    private void updateStatistics() {
        // Update total recharges count
        if (totalRechargesCount != null) {
            int totalRecharges = preferences.getInt("total_recharges", 0);
            totalRechargesCount.setText(String.valueOf(totalRecharges));
        }

        // Update success rate
        if (successRateText != null) {
            int successRate = preferences.getInt("success_rate", 100);
            successRateText.setText(successRate + "%");

            // Color based on success rate
            if (successRate >= 90) {
                successRateText.setTextColor(getResources().getColor(android.R.color.holo_green_dark));
            } else if (successRate >= 70) {
                successRateText.setTextColor(getResources().getColor(android.R.color.holo_orange_dark));
            } else {
                successRateText.setTextColor(getResources().getColor(android.R.color.holo_red_dark));
            }
        }

        // Update uptime
        if (uptimeText != null) {
            long startTime = preferences.getLong("service_start_time", System.currentTimeMillis());
            long uptime = System.currentTimeMillis() - startTime;
            String uptimeString = formatUptime(uptime);
            uptimeText.setText(uptimeString);
        }
    }

    private String formatUptime(long uptimeMillis) {
        long hours = uptimeMillis / (1000 * 60 * 60);
        long days = hours / 24;

        if (days > 0) {
            return days + "d";
        } else if (hours > 0) {
            return hours + "h";
        } else {
            long minutes = uptimeMillis / (1000 * 60);
            return Math.max(1, minutes) + "m";
        }
    }

    @Override
    public void onResume() {
        super.onResume();
        // Refresh data when fragment becomes visible
        loadDashboardData();
    }
}
