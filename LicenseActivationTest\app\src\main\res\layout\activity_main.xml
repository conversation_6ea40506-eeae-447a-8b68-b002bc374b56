<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/activity_main"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@drawable/bg"
    android:keepScreenOn="true"
    android:orientation="vertical">
<include
    android:id="@+id/license_activation"
    layout="@layout/license_activation"
    android:visibility="gone"
    />

    <!-- Top Navigation Bar -->
    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/btnSpeakContainer"
        android:layout_width="match_parent"
        android:layout_height="56dp"
        android:background="#3F51B5"
        android:elevation="8dp">

        <!-- Home Tab (Active) -->
        <LinearLayout
            android:id="@+id/home"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:background="#4A5DC7"
            android:clickable="true"
            android:focusable="true"
            android:foreground="?attr/selectableItemBackground"
            android:gravity="center"
            android:orientation="horizontal"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toStartOf="@id/bank"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintHorizontal_weight="1"
            app:layout_constraintHorizontal_chainStyle="spread">

            <ImageView
                android:layout_width="24dp"
                android:layout_height="24dp"
                android:src="@drawable/ic_home_black_24dp"
                app:tint="#FFFFFF" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="8dp"
                android:text="Home"
                android:textColor="#FFFFFF"
                android:textSize="14sp"
                android:textStyle="bold" />
        </LinearLayout>

        <!-- Banking Tab -->
        <LinearLayout
            android:id="@+id/bank"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:background="@null"
            android:clickable="true"
            android:focusable="true"
            android:foreground="?attr/selectableItemBackground"
            android:gravity="center"
            android:orientation="horizontal"
            app:layout_constraintStart_toEndOf="@id/home"
            app:layout_constraintEnd_toStartOf="@id/sms"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintHorizontal_weight="1">

            <ImageView
                android:layout_width="24dp"
                android:layout_height="24dp"
                android:src="@drawable/ic_settings_cell_black_24dp"
                app:tint="#FFFFFF" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="8dp"
                android:text="Banking"
                android:textColor="#FFFFFF"
                android:textSize="14sp" />
        </LinearLayout>

        <!-- Settings Tab -->
        <LinearLayout
            android:id="@+id/sms"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:background="@null"
            android:clickable="true"
            android:focusable="true"
            android:foreground="?attr/selectableItemBackground"
            android:gravity="center"
            android:orientation="horizontal"
            app:layout_constraintStart_toEndOf="@id/bank"
            app:layout_constraintEnd_toStartOf="@id/mon"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintHorizontal_weight="1">

            <ImageView
                android:layout_width="24dp"
                android:layout_height="24dp"
                android:src="@drawable/ic_settings_black_24dp"
                app:tint="#FFFFFF" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="8dp"
                android:text="Settings"
                android:textColor="#FFFFFF"
                android:textSize="14sp" />
        </LinearLayout>

        <!-- Monitor Tab -->
        <LinearLayout
            android:id="@+id/mon"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:background="@null"
            android:clickable="true"
            android:focusable="true"
            android:foreground="?attr/selectableItemBackground"
            android:gravity="center"
            android:orientation="horizontal"
            app:layout_constraintStart_toEndOf="@id/sms"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintHorizontal_weight="1">

            <ImageView
                android:layout_width="24dp"
                android:layout_height="24dp"
                android:src="@drawable/ic_baseline_add_to_queue_24"
                app:tint="#FFFFFF" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="8dp"
                android:text="Monitor"
                android:textColor="#FFFFFF"
                android:textSize="14sp" />
        </LinearLayout>
    </androidx.constraintlayout.widget.ConstraintLayout>

    <ScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_marginLeft="8dp"
        android:layout_marginRight="8dp"
        android:paddingLeft="8dp"
        android:paddingRight="8dp">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical">

            <!-- App Logo and Title -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:gravity="center"
                android:orientation="vertical"
                android:padding="16dp">

                <ImageView
                    android:id="@+id/imageView"
                    android:layout_width="80dp"
                    android:layout_height="80dp"
                    android:layout_marginTop="16dp"
                    app:srcCompat="@mipmap/ic_launcher" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center_horizontal"
                    android:layout_marginTop="16dp"
                    android:layout_marginBottom="8dp"
                    android:text="Automatic Mobile Recharge System"
                    android:textAppearance="?android:attr/textAppearanceMedium"
                    android:textColor="#FFFFFF"
                    android:textStyle="bold" />

                <TextView
                    android:id="@+id/myid"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center_horizontal"
                    android:layout_marginTop="8dp"
                    android:layout_marginBottom="16dp"
                    android:background="@drawable/rounded_cornerss"
                    android:padding="8dp"
                    android:text="Device ID: **********"
                    android:textAppearance="?android:attr/textAppearanceMedium"
                    android:textColor="#F42121"
                    android:textStyle="bold" />
            </LinearLayout>

            <!-- License Key Section -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_margin="8dp"
                android:background="@drawable/rounded_corner"
                android:elevation="4dp"
                android:orientation="vertical"
                android:padding="16dp">

                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="8dp"
                    android:text="License Information"
                    android:textColor="#3F51B5"
                    android:textSize="18sp"
                    android:textStyle="bold" />

                <EditText
                    android:id="@+id/url"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:background="@drawable/rounded_cornerss"
                    android:drawableStart="@drawable/ic_settings_black_24dp"
                    android:drawablePadding="8dp"
                    android:drawableTint="#3F51B5"
                    android:hint="License Key eg: xxxxx-xxxxx-xxxxx-xxxxx"
                    android:padding="12dp"
                    android:textAppearance="?android:attr/textAppearanceMedium"
                    android:textColor="#ff111111" />

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="8dp"
                    android:gravity="center_vertical"
                    android:orientation="horizontal">

                    <EditText
                        android:id="@+id/dpin"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:background="@drawable/rounded_cornerss"
                        android:drawableStart="@drawable/ic_settings_cell_black_24dp"
                        android:drawablePadding="8dp"
                        android:hint="PIN"
                        android:inputType="numberPassword"
                        android:padding="12dp"
                        android:text="1234"
                        android:textAppearance="?android:attr/textAppearanceMedium"
                        android:textColor="#ff111111" />

                    <RadioGroup
                        android:id="@+id/server_type"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="8dp"
                        android:orientation="horizontal">

                        <RadioButton
                            android:id="@+id/olmd"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:checked="true"
                            android:paddingRight="15dp"
                            android:text="V.1"
                            android:textColor="#3F51B5" />

                        <RadioButton
                            android:id="@+id/newm"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:paddingRight="15dp"
                            android:text="V.2"
                            android:textColor="#3F51B5" />
                    </RadioGroup>

                    <ToggleButton
                        android:id="@+id/sav"
                        android:layout_width="50dp"
                        android:layout_height="30dp"
                        android:layout_marginStart="8dp"
                        android:background="@drawable/toggle_selector"
                        android:checked="false"
                        android:text=""
                        android:textOff=""
                        android:textOn="" />
                </LinearLayout>
            </LinearLayout>

            <!-- SIM Settings -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_margin="8dp"
                android:background="@drawable/rounded_corner"
                android:elevation="4dp"
                android:orientation="vertical"
                android:padding="16dp">

                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="8dp"
                    android:text="Network Settings"
                    android:textColor="#3F51B5"
                    android:textSize="18sp"
                    android:textStyle="bold" />

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="8dp"
                    android:background="@drawable/rounded_cornerss"
                    android:gravity="center_vertical"
                    android:orientation="horizontal"
                    android:padding="8dp">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginEnd="8dp"
                        android:text="SIM A:"
                        android:textColor="#3F51B5"
                        android:textStyle="bold" />

                    <Spinner
                        android:id="@+id/simaname"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="8dp"
                        android:layout_marginBottom="8dp"
                        android:background="@android:color/white"
                        android:padding="8dp" />
                </LinearLayout>

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:background="@drawable/rounded_cornerss"
                    android:gravity="center_vertical"
                    android:orientation="horizontal"
                    android:padding="8dp">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginEnd="8dp"
                        android:text="SIM B:"
                        android:textColor="#3F51B5"
                        android:textStyle="bold" />

                    <Spinner
                        android:id="@+id/simbname"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="8dp"
                        android:layout_marginBottom="8dp"
                        android:background="@android:color/white"
                        android:padding="8dp" />
                </LinearLayout>
            </LinearLayout>

            <!-- System Settings -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_margin="8dp"
                android:background="@drawable/rounded_corner"
                android:elevation="4dp"
                android:orientation="vertical"
                android:padding="16dp">

                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="16dp"
                    android:text="System Settings"
                    android:textColor="#3F51B5"
                    android:textSize="18sp"
                    android:textStyle="bold" />

                <!-- SMS Service -->
                <RelativeLayout
                    android:id="@+id/simsms"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="8dp"
                    android:background="@drawable/rounded_cornerss"
                    android:padding="12dp">

                    <TextView
                        android:id="@+id/simbc"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_centerVertical="true"
                        android:text="SMS Service"
                        android:textAppearance="?android:attr/textAppearanceMedium"
                        android:textColor="#ff090909" />

                    <ToggleButton
                        android:id="@+id/sms_service"
                        android:layout_width="50dp"
                        android:layout_height="30dp"
                        android:layout_alignParentEnd="true"
                        android:layout_centerVertical="true"
                        android:background="@drawable/toggle_selector"
                        android:checked="false"
                        android:text=""
                        android:textOff=""
                        android:textOn="" />
                </RelativeLayout>

                <!-- Accessibility -->
                <RelativeLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="8dp"
                    android:background="@drawable/rounded_cornerss"
                    android:padding="12dp">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_centerVertical="true"
                        android:text="Accessibility"
                        android:textAppearance="?android:attr/textAppearanceMedium"
                        android:textColor="#ff080908" />

                    <ToggleButton
                        android:id="@+id/auto"
                        android:layout_width="50dp"
                        android:layout_height="30dp"
                        android:layout_alignParentEnd="true"
                        android:layout_centerVertical="true"
                        android:background="@drawable/toggle_selector"
                        android:checked="false"
                        android:text=""
                        android:textOff=""
                        android:textOn="" />
                </RelativeLayout>

                <!-- Power Optimize -->
                <RelativeLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="8dp"
                    android:background="@drawable/rounded_cornerss"
                    android:padding="12dp">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_centerVertical="true"
                        android:text="Power Optimize"
                        android:textAppearance="?android:attr/textAppearanceMedium"
                        android:textColor="#ff080908" />

                    <ToggleButton
                        android:id="@+id/bettery"
                        android:layout_width="50dp"
                        android:layout_height="30dp"
                        android:layout_alignParentEnd="true"
                        android:layout_centerVertical="true"
                        android:background="@drawable/toggle_selector"
                        android:checked="false"
                        android:text=""
                        android:textOff=""
                        android:textOn="" />
                </RelativeLayout>

                <!-- Find Page -->
                <RelativeLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:background="@drawable/rounded_cornerss"
                    android:padding="12dp">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_centerVertical="true"
                        android:text="Find Page"
                        android:textAppearance="?android:attr/textAppearanceMedium"
                        android:textColor="#ff080908" />

                    <ToggleButton
                        android:id="@+id/next"
                        android:layout_width="50dp"
                        android:layout_height="30dp"
                        android:layout_alignParentEnd="true"
                        android:layout_centerVertical="true"
                        android:background="@drawable/toggle_selector"
                        android:checked="false"
                        android:text=""
                        android:textOff=""
                        android:textOn="" />
                </RelativeLayout>
            </LinearLayout>
        </LinearLayout>
    </ScrollView>
</LinearLayout>
