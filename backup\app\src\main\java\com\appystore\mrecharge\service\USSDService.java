package com.appystore.mrecharge.service;


import android.accessibilityservice.AccessibilityService;
import android.accessibilityservice.AccessibilityServiceInfo;
import android.content.Context;
import android.content.SharedPreferences;
import android.database.sqlite.SQLiteDatabase;
import android.preference.PreferenceManager;
import android.text.TextUtils;
import android.util.Log;
import android.view.accessibility.AccessibilityNodeInfo;
import android.widget.Button;
import android.widget.ScrollView;
import android.widget.TextView;
import com.appystore.mrecharge.DbHelper;
import com.appystore.mrecharge.Dialfunction;
import java.io.IOException;
import java.io.LineNumberReader;
import java.io.StringReader;
import java.util.Iterator;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/* loaded from: classes.dex */
public class USSDService extends AccessibilityService {
    public static String TAG = USSDService.class.getSimpleName();
    private SQLiteDatabase database;
    boolean flag;
    AccessibilityNodeInfo inputNode;
    int jo;
    String job;
    String jobb;
    private DbHelper mydb;
    private Dialfunction myser;
    String s;
    SharedPreferences sett;
    SharedPreferences setting;
    AccessibilityNodeInfo source;
    String text;
    Context context = this;
    boolean next = false;
    int jobcount = 1;
    boolean back = false;
    int linr = 1;
    String id = "0";

    @Override // android.accessibilityservice.AccessibilityService
    public void onInterrupt() {
    }

    @Override
    public void onAccessibilityEvent(android.view.accessibility.AccessibilityEvent event) {
        try {
            if (event == null) {
                return;
            }

            Log.d(TAG, "onAccessibilityEvent: " + event.getEventType());

            // Only process window content changed and window state changed events
            if (event.getEventType() != android.view.accessibility.AccessibilityEvent.TYPE_WINDOW_CONTENT_CHANGED &&
                event.getEventType() != android.view.accessibility.AccessibilityEvent.TYPE_WINDOW_STATE_CHANGED) {
                return;
            }

            // Get the source node
            AccessibilityNodeInfo source = event.getSource();
            if (source == null) {
                return;
            }

            // Check if this is a USSD dialog
            if (isUssdDialog(source)) {
                Log.d(TAG, "USSD dialog detected");
                handleUssdDialog(source);
            }

        } catch (Exception e) {
            Log.e(TAG, "Error in onAccessibilityEvent", e);
        }
    }

    /**
     * Check if the current window is a USSD dialog
     */
    /**
     * Extracts text from an AccessibilityNodeInfo and its children recursively
     * @param node The root node to extract text from
     * @return Concatenated text from all text nodes
     */
    private String extractTextFromNode(AccessibilityNodeInfo node) {
        if (node == null) {
            return "";
        }

        StringBuilder stringBuilder = new StringBuilder();
        
        // Get the node's text if available
        if (node.getText() != null) {
            String nodeText = node.getText().toString().trim();
            if (!nodeText.isEmpty()) {
                if (stringBuilder.length() > 0) {
                    stringBuilder.append(" ");
                }
                stringBuilder.append(nodeText);
            }
        }

        // Get the node's content description if available
        if (node.getContentDescription() != null) {
            String contentDesc = node.getContentDescription().toString().trim();
            if (!contentDesc.isEmpty()) {
                if (stringBuilder.length() > 0) {
                    stringBuilder.append(" ");
                }
                stringBuilder.append(contentDesc);
            }
        }

        // Recursively process child nodes
        for (int i = 0; i < node.getChildCount(); i++) {
            AccessibilityNodeInfo child = node.getChild(i);
            if (child != null) {
                String childText = extractTextFromNode(child);
                if (!childText.isEmpty()) {
                    if (stringBuilder.length() > 0) {
                        stringBuilder.append(" ");
                    }
                    stringBuilder.append(childText);
                }
                child.recycle();
            }
        }

        return stringBuilder.toString().trim();
    }

    /**
     * Checks if the current window is a USSD dialog
     */
    private boolean isUssdDialog(AccessibilityNodeInfo source) {
        try {
            // Look for USSD dialog indicators
            String packageName = source.getPackageName() != null ? source.getPackageName().toString() : "";

            // Common USSD dialog package names
            if (packageName.contains("com.android.phone") ||
                packageName.contains("com.android.dialer") ||
                packageName.contains("ussd")) {

                // Look for text content that indicates USSD response
                String dialogText = extractTextFromNode(source);
                if (dialogText != null && (
                    dialogText.toLowerCase().contains("ussd") ||
                    dialogText.toLowerCase().contains("balance") ||
                    dialogText.toLowerCase().contains("recharge") ||
                    dialogText.toLowerCase().contains("tk") ||
                    dialogText.toLowerCase().contains("taka") ||
                    dialogText.toLowerCase().contains("successful") ||
                    dialogText.toLowerCase().contains("failed"))) {
                    return true;
                }
            }

            return false;
        } catch (Exception e) {
            Log.e(TAG, "Error checking USSD dialog", e);
            return false;
        }
    }

    /**
     * Handle USSD dialog response
     */
    private void handleUssdDialog(AccessibilityNodeInfo source) {
        try {
            // Extract text from the dialog
            String ussdResponse = extractTextFromNode(source);

            if (ussdResponse != null && !ussdResponse.trim().isEmpty()) {
                Log.d(TAG, "USSD Response: " + ussdResponse);

                // Process the USSD response
                processUssdResponse(ussdResponse);

                // Try to dismiss the dialog
                dismissUssdDialog(source);
            }

        } catch (Exception e) {
            Log.e(TAG, "Error handling USSD dialog", e);
        }
    }

    /**
     * Process USSD response and update database
     */
    private void processUssdResponse(String response) {
        try {
            // Get current order ID from preferences
            String orderId = getPref("main_id", this);

            if (orderId != null && !orderId.equals("0") && !orderId.equals("t")) {
                Log.d(TAG, "Processing USSD response for order: " + orderId);

                // Determine if recharge was successful
                boolean isSuccessful = isRechargeSuccessful(response);
                String status = isSuccessful ? "completed" : "failed";

                // Update database
                if (mydb != null) {
                    mydb.updateRechargeStatus(orderId, status, response);
                }

                // Send response to server
                if (myser != null) {
                    myser.sendResponse(orderId, response, status);
                }

                // Clear the order ID
                SavePreferences("main_id", "0");

                // Clear busy flag
                SharedPreferences.Editor edit = getSharedPreferences("serv", 0).edit();
                edit.putInt("busy", 0);
                edit.apply();

                Log.d(TAG, "USSD response processed successfully");
            }

        } catch (Exception e) {
            Log.e(TAG, "Error processing USSD response", e);
        }
    }

    /**
     * Determine if recharge was successful based on response text
     */
    private boolean isRechargeSuccessful(String response) {
        if (response == null) {
            return false;
        }

        String lowerResponse = response.toLowerCase();

        // Success indicators
        if (lowerResponse.contains("successful") ||
            lowerResponse.contains("success") ||
            lowerResponse.contains("completed") ||
            lowerResponse.contains("recharged") ||
            lowerResponse.contains("credited")) {
            return true;
        }

        // Failure indicators
        if (lowerResponse.contains("failed") ||
            lowerResponse.contains("error") ||
            lowerResponse.contains("invalid") ||
            lowerResponse.contains("insufficient") ||
            lowerResponse.contains("expired")) {
            return false;
        }

        // If contains balance or amount, likely successful
        if (lowerResponse.contains("balance") ||
            lowerResponse.contains("tk") ||
            lowerResponse.contains("taka")) {
            return true;
        }

        // Default to false for unknown responses
        return false;
    }

    /**
     * Try to dismiss USSD dialog
     */
    private void dismissUssdDialog(AccessibilityNodeInfo source) {
        try {
            // Look for OK, Cancel, or Close buttons
            AccessibilityNodeInfo okButton = findButtonByText(source, "ok");
            if (okButton == null) {
                okButton = findButtonByText(source, "cancel");
            }
            if (okButton == null) {
                okButton = findButtonByText(source, "close");
            }

            if (okButton != null && okButton.isClickable()) {
                okButton.performAction(AccessibilityNodeInfo.ACTION_CLICK);
                Log.d(TAG, "USSD dialog dismissed");
            }

        } catch (Exception e) {
            Log.e(TAG, "Error dismissing USSD dialog", e);
        }
    }

    /**
     * Find button by text content
     */
    private AccessibilityNodeInfo findButtonByText(AccessibilityNodeInfo root, String buttonText) {
        try {
            if (root == null) {
                return null;
            }

            // Check current node
            if (root.getClassName() != null &&
                root.getClassName().toString().contains("Button")) {
                CharSequence text = root.getText();
                if (text != null && text.toString().toLowerCase().contains(buttonText.toLowerCase())) {
                    return root;
                }
            }

            // Check children recursively
            for (int i = 0; i < root.getChildCount(); i++) {
                AccessibilityNodeInfo child = root.getChild(i);
                if (child != null) {
                    AccessibilityNodeInfo result = findButtonByText(child, buttonText);
                    if (result != null) {
                        return result;
                    }
                }
            }

            return null;
        } catch (Exception e) {
            Log.e(TAG, "Error finding button", e);
            return null;
        }
    }

    private String testLineExtract(String str, int i, int i2) throws IOException {
        int i3;
        LineNumberReader lineNumberReader = new LineNumberReader(new StringReader(capitalize(str)));
        while (true) {
            String readLine = lineNumberReader.readLine();
            if (readLine == null) {
                break;
            }
            if (lineNumberReader.getLineNumber() != i2) {
                Matcher matcher = Pattern.compile("-?\\d+").matcher(readLine);
                while (matcher.find()) {
                    Log.d("bod", matcher.group());
                    int i4 = 0;
                    try {
                        i3 = Integer.parseInt(matcher.group().toString());
                    } catch (NumberFormatException unused) {
                        i3 = 0;
                    }
                    if (i3 == i) {
                        String str2 = i3 + "tk";
                        String str3 = "tk" + i3;
                        String tkfindfinall = tkfindfinall(readLine.toLowerCase());
                        if (tkfindfinall.equals(str2) || tkfindfinall.equals(str3)) {
                            if (TextUtils.isEmpty(deffind(readLine, "Main", i)) && TextUtils.isEmpty(deffind(readLine, "Default", i))) {
                                String replaceAll = readLine.split("\\s+")[0].replaceAll("\\D+", "");
                                try {
                                    i4 = Integer.parseInt(replaceAll);
                                } catch (NumberFormatException unused2) {
                                }
                                if (i4 == i) {
                                    break;
                                }
                                return replaceAll;
                            }
                        }
                    }
                }
            }
        }
        return null;
    }

    private String fetchResponse(AccessibilityNodeInfo accessibilityNodeInfo) {
        if (accessibilityNodeInfo == null) {
            return "";
        }
        String str = "";
        for (int i = 0; i < accessibilityNodeInfo.getChildCount(); i++) {
            AccessibilityNodeInfo child = accessibilityNodeInfo.getChild(i);
            if (child != null) {
                CharSequence text = child.getText();
                if (text != null && child.getClassName().equals(Button.class.getName())) {
                    if (text.toString().toLowerCase().equals("ok") || text.toString().toLowerCase().equals("cancel")) {
                        return str;
                    }
                } else if (text != null && child.getClassName().equals(TextView.class.getName())) {
                    if (text.toString().length() > 10) {
                        str = text.toString();
                    }
                } else if (child.getClassName().equals(ScrollView.class.getName())) {
                    String str2 = str;
                    for (int i2 = 0; i2 < child.getChildCount(); i2++) {
                        AccessibilityNodeInfo child2 = child.getChild(i2);
                        CharSequence text2 = child2.getText();
                        if (text2 != null && child2.getClassName().equals(TextView.class.getName())) {
                            if (text2.toString().length() > 10) {
                                str2 = text2.toString();
                            }
                        } else if (text2 != null && child2.getClassName().equals(Button.class.getName()) && (text2.toString().toLowerCase().equals("ok") || text2.toString().toLowerCase().equals("cancel"))) {
                            return str2;
                        }
                    }
                    str = str2;
                } else {
                    continue;
                }
            }
        }
        return str;
    }

    private String processUSSDText(List<CharSequence> list) {
        Iterator<CharSequence> it = list.iterator();
        if (it.hasNext()) {
            return String.valueOf(it.next());
        }
        return null;
    }

    @Override // android.accessibilityservice.AccessibilityService
    protected void onServiceConnected() {
        super.onServiceConnected();
        Log.d(TAG, "onServiceConnected");

        // Initialize database helper and dial function
        try {
            mydb = new DbHelper(this);
            myser = new Dialfunction(this);
            Log.d(TAG, "Database helper and dial function initialized");
        } catch (Exception e) {
            Log.e(TAG, "Error initializing components", e);
        }

        AccessibilityServiceInfo accessibilityServiceInfo = new AccessibilityServiceInfo();
        accessibilityServiceInfo.flags = 1;
        accessibilityServiceInfo.packageNames = new String[]{"com.android.phone", "com.android.dialer"};
        accessibilityServiceInfo.eventTypes = android.view.accessibility.AccessibilityEvent.TYPE_WINDOW_CONTENT_CHANGED |
                                            android.view.accessibility.AccessibilityEvent.TYPE_WINDOW_STATE_CHANGED;
        accessibilityServiceInfo.feedbackType = 16;
        setServiceInfo(accessibilityServiceInfo);
    }

    private String morefind(String str, String str2) throws IOException {
        LineNumberReader lineNumberReader = new LineNumberReader(new StringReader(capitalize(str)));
        String str3 = null;
        while (true) {
            String readLine = lineNumberReader.readLine();
            if (readLine == null) {
                return str3;
            }
            String capitalize = capitalize(readLine);
            Log.d("good", "" + capitalize);
            if (capitalize.equals("*. back00. next")) {
                str3 = "00";
            } else if (capitalize.indexOf(str2) >= 0) {
                return readLine.split("\\s+")[0].replaceAll("\\.", "").replaceAll("\\s+", "");
            }
        }
    }

    private String deffind(String str, String str2, int i) throws IOException {
        String capitalize = capitalize(str);
        String capitalize2 = capitalize(str2);
        LineNumberReader lineNumberReader = new LineNumberReader(new StringReader(capitalize));
        while (true) {
            String readLine = lineNumberReader.readLine();
            if (readLine == null) {
                return null;
            }
            if (capitalize(readLine).indexOf(capitalize2) >= 0) {
                Matcher matcher = Pattern.compile("-?\\d+").matcher(readLine);
                while (matcher.find()) {
                    int intValue = new Integer(matcher.group().toString()).intValue();
                    if (intValue == i) {
                        if (readLine.toLowerCase().indexOf(intValue + "tk") >= 0) {
                            return readLine.split("\\s+")[0].replaceAll("\\.", "").replaceAll("\\s+", "");
                        }
                    }
                }
            }
        }
    }

    private String rcfind(String str, String str2, int i) throws IOException {
        String capitalize = capitalize(str);
        String capitalize2 = capitalize(str2);
        LineNumberReader lineNumberReader = new LineNumberReader(new StringReader(capitalize));
        while (true) {
            String readLine = lineNumberReader.readLine();
            if (readLine == null) {
                return null;
            }
            if (capitalize(readLine).indexOf(capitalize2) >= 0) {
                Matcher matcher = Pattern.compile("-?\\d+").matcher(readLine);
                while (matcher.find()) {
                    int intValue = new Integer(matcher.group().toString()).intValue();
                    if (intValue == i) {
                        if (readLine.toLowerCase().indexOf("tk " + intValue) >= 0) {
                            return readLine.split("\\s+")[0].replaceAll("\\.", "").replaceAll("\\s+", "");
                        }
                    }
                }
            }
        }
    }

    private String capitalize(String str) {
        return (str == null || str.length() == 0) ? "" : str.toLowerCase();
    }

    public boolean haveblank(String str) {
        return TextUtils.isEmpty(str);
    }

    public static String getPref(String str, Context context) {
        return PreferenceManager.getDefaultSharedPreferences(context).getString(str, "t");
    }

    public static String getPrefd(String str, Context context) {
        return PreferenceManager.getDefaultSharedPreferences(context).getString(str, "8888888888888888");
    }

    public void SavePreferences(String str, String str2) {
        SharedPreferences.Editor edit = PreferenceManager.getDefaultSharedPreferences(getApplicationContext()).edit();
        edit.putString(str, str2);
        edit.commit();
    }

    public void ClearPreferences(String str) {
        PreferenceManager.getDefaultSharedPreferences(getApplicationContext()).edit().remove(str).commit();
    }

    private String tkfind(String str) {
        Matcher matcher = Pattern.compile("(tk)(\\d+)").matcher(str);
        if (matcher.find()) {
            return matcher.group();
        }
        return null;
    }

    private String tkfind2(String str) {
        Matcher matcher = Pattern.compile("(\\d+)(tk)").matcher(str);
        if (matcher.find()) {
            return matcher.group();
        }
        return null;
    }

    private String tkfindfinall(String str) {
        String tkfind2;
        if (!TextUtils.isEmpty(tkfind(str))) {
            tkfind2 = tkfind(str);
        } else {
            tkfind2 = tkfind2(str);
        }
        return !TextUtils.isEmpty(tkfind2) ? tkfind2.toLowerCase() : "osman";
    }
}